package defpackage;

import android.content.Context;
import dagger.android.AndroidInjector;
import dagger.internal.DoubleCheck;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import j$.util.Optional;
import java.util.Map;

/* compiled from: :com.google.android.gms@252431029@25.24.31 (190400-772615181) */
/* loaded from: classes8.dex */
final class dvpv implements AndroidInjector {
    final Provider a;
    final Provider b;
    final Provider c;
    final Provider d;
    final Provider e;
    final Provider f;
    final Provider g;
    final Provider h;
    final Provider i;
    final Provider j;
    final Provider k;
    final Provider l;
    final Provider m;
    final Provider n;
    final Provider o;
    final Provider p;
    final Provider q;
    private final djan r;
    private final djcg s;
    private final djch t;
    private final dhdr u;

    public dvpv(djan djanVar, dhdr dhdrVar, djch djchVar, djcg djcgVar, dxiy dxiyVar) {
        this.r = djanVar;
        this.s = djcgVar;
        this.t = djchVar;
        this.u = dhdrVar;
        dvpt dvptVar = new dvpt(djcgVar);
        this.a = dvptVar;
        dvps dvpsVar = new dvps(djcgVar);
        this.b = dvpsVar;
        dwvq dwvqVar = new dwvq(dvptVar, dvpsVar);
        this.c = dwvqVar;
        this.d = new dwsf();
        dwse dwseVar = new dwse(dvptVar);
        this.e = dwseVar;
        Provider provider = dvpw.a;
        this.f = provider;
        dwsd dwsdVar = new dwsd(dvptVar, provider);
        this.g = dwsdVar;
        dwsq dwsqVar = new dwsq(dvptVar, dwvqVar, dwseVar, dwsdVar, provider);
        this.h = dwsqVar;
        dwsc dwscVar = new dwsc(dwsqVar, dwsdVar);
        this.i = dwscVar;
        dhdy dhdyVar = new dhdy();
        this.j = dhdyVar;
        this.k = new dwry(dwscVar, dhdyVar, dwvqVar);
        dxmk dxmkVar = new dxmk(dvptVar);
        this.l = dxmkVar;
        dhhg dhhgVar = new dhhg(dvptVar);
        this.m = dhhgVar;
        dvpu dvpuVar = new dvpu(dxiyVar);
        this.n = dvpuVar;
        dxls dxlsVar = new dxls(dxmkVar, dhhgVar, dvpuVar);
        this.o = dxlsVar;
        this.p = DoubleCheck.b(new dxlp(dxlsVar));
        this.q = DoubleCheck.b(dxln.a);
    }

    final dhdi a() {
        this.s.a();
        return new dhdi(djar.b(this.r), dhdx.a());
    }

    final dhef b() {
        djan djanVar = this.r;
        return dhee.b(this.s.a(), djar.b(djanVar), djaq.b(djanVar), dhdx.a(), this.u.a, Optional.empty());
    }

    final dhqi c() {
        djcg djcgVar = this.s;
        empk empkVarK = k();
        Context contextA = djcgVar.a();
        djan djanVar = this.r;
        dgkt dgktVar = new dgkt(contextA, djaq.b(djanVar), dhdx.a());
        Context contextA2 = djcgVar.a();
        String strB = djaq.b(djanVar);
        String strA = dhdx.a();
        Context contextA3 = djcgVar.a();
        String strB2 = djaq.b(djanVar);
        String strA2 = dhdx.a();
        dutt duttVar = new dutt(djcgVar.a(), djaq.b(djanVar), dhdx.a());
        int i = dust.a;
        return new dhqi(empkVarK, dgktVar, new dhps(contextA2, strB, strA, new dutl(new dutq(contextA3, strB2, strA2, duttVar))), new dhqn(b(), new eund(djcgVar.a(), dxnu.b(dxnp.a(dhdv.e(dhdx.a())), djap.b(djanVar))), new dxhb(new dhhf(djcgVar.a()), (dxlq) this.p.get(), (dxlm) this.q.get())), new azcg(1, 10), duur.a(), d(), g(), new dsqj(d()), new dhql(djap.b(djanVar), k(), new dxyk(djcgVar.a(), djar.b(djanVar), dhdx.a())));
    }

    final dsqg d() {
        return new dsqg(this.s.a(), djaq.b(this.r), dhdx.a());
    }

    final dsqn e() {
        dhef dhefVarB = b();
        abbm abbmVar = new abbm(this.s.a());
        djan djanVar = this.r;
        return new dsqn(dhefVarB, new dgme(abbmVar, djar.b(djanVar), djaq.b(djanVar)));
    }

    final dsqp f() {
        return new dsqp(this.s.a(), djaq.b(this.r), dhdx.a());
    }

    final dsqr g() {
        return new dsqr(this.s.a(), djaq.b(this.r), dhdx.a());
    }

    final dsrw h() {
        return new dsrw(djap.b(this.r), d(), f(), g(), e(), a(), j(), c(), duur.a());
    }

    final dsyl i() {
        djcg djcgVar = this.s;
        gegm gegmVarP = djcgVar.p();
        Preconditions.e(gegmVarP);
        azcg azcgVar = new azcg(Integer.MAX_VALUE, 9);
        dslt dsltVarC = djcgVar.c();
        Preconditions.e(dsltVarC);
        dsma dsmaVarE = djcgVar.e();
        Preconditions.e(dsmaVarE);
        dsmg dsmgVarF = djcgVar.f();
        Preconditions.e(dsmgVarF);
        dslw dslwVarD = djcgVar.d();
        Preconditions.e(dslwVarD);
        empk empkVarK = k();
        Map map = dwsa.a;
        return new dsyl(gegmVarP, azcgVar, dsltVarC, dsmaVarE, dsmgVarF, dslwVarD, empkVarK, ((dwry) this.k).get(), djar.b(this.r), a());
    }

    @Override // dagger.android.AndroidInjector
    public final /* synthetic */ void inject(Object obj) {
        dvys dvysVar = (dvys) obj;
        dvysVar.a = new dstu(djap.b(this.r), new dsqb(i(), new dspt(i(), h()), new dxgo(this.s.a(), dhdx.a(), duuo.a()), new dsdv(), new azcg(Integer.MAX_VALUE, 10)), e(), d(), f(), g(), h(), c(), duur.a(), new azcg(Integer.MAX_VALUE, 10), new azcg(Integer.MAX_VALUE, 9), j());
        dhdi dhdiVarA = a();
        fyfp fyfpVarA = dwsg.a();
        Map map = dwsa.a;
        dvysVar.b = new dvxc(dhdiVarA, fyfa.c(fyfpVarA));
    }

    final dvtj j() {
        return new dvtj(this.s.h());
    }

    final empk k() {
        return new empk(this.s.a());
    }
}
