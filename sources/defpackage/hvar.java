package defpackage;

import java.math.BigInteger;
import java.util.Enumeration;

/* compiled from: :com.google.android.gms@252431029@25.24.31 (190400-772615181) */
/* loaded from: classes12.dex */
public final class hvar extends huvu {
    final huvq a;
    final huvq b;
    final huvq c;

    public hvar(BigInteger bigInteger, BigInteger bigInteger2, int i) {
        this.a = new huvq(bigInteger);
        this.b = new huvq(bigInteger2);
        this.c = i != 0 ? new huvq(i) : null;
    }

    public static hvar d(Object obj) {
        if (obj instanceof hvar) {
            return (hvar) obj;
        }
        if (obj != null) {
            return new hvar(huwm.m(obj));
        }
        return null;
    }

    public final BigInteger a() {
        return this.b.j();
    }

    public final BigInteger b() {
        huvq huvqVar = this.c;
        if (huvqVar == null) {
            return null;
        }
        return huvqVar.j();
    }

    public final BigInteger c() {
        return this.a.j();
    }

    @Override // defpackage.huvu, defpackage.huva
    public final huwf p() {
        huvb huvbVar = new huvb(3);
        huvbVar.b(this.a);
        huvbVar.b(this.b);
        if (b() != null) {
            huvbVar.b(this.c);
        }
        return new huxz(huvbVar);
    }

    private hvar(huwm huwmVar) {
        Enumeration enumerationF = huwmVar.f();
        this.a = huvq.n(enumerationF.nextElement());
        this.b = huvq.n(enumerationF.nextElement());
        this.c = enumerationF.hasMoreElements() ? (huvq) enumerationF.nextElement() : null;
    }
}
