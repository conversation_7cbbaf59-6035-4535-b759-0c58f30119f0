#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint {{projectName}}.podspec` to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = '{{projectName}}'
  s.version          = '0.0.1'
  s.summary          = '{{description}}'
  s.description      = <<-DESC
{{description}}
                       DESC
  s.homepage         = 'http://example.com'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'Your Company' => '<EMAIL>' }
  s.source           = { :path => '.' }
  {{#withSwiftPackageManager}}
  s.source_files = '{{projectName}}/Sources/{{projectName}}/**/*'
  {{/withSwiftPackageManager}}
  {{^withSwiftPackageManager}}
  s.source_files = 'Classes/**/*'
  {{/withSwiftPackageManager}}
  s.dependency 'Flutter'
  s.platform = :ios, '12.0'

  # Flutter.framework does not contain a i386 slice.
  s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES', 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386' }
  s.swift_version = '5.0'

  # If your plugin requires a privacy manifest, for example if it uses any
  # required reason APIs, update the PrivacyInfo.xcprivacy file to describe your
  # plugin's privacy impact, and then uncomment this line. For more information,
  # see https://developer.apple.com/documentation/bundleresources/privacy_manifest_files
  {{#withSwiftPackageManager}}
  # s.resource_bundles = {'{{projectName}}_privacy' => ['{{projectName}}/Sources/{{projectName}}/PrivacyInfo.xcprivacy']}
  {{/withSwiftPackageManager}}
  {{^withSwiftPackageManager}}
  # s.resource_bundles = {'{{projectName}}_privacy' => ['Resources/PrivacyInfo.xcprivacy']}
  {{/withSwiftPackageManager}}
end
